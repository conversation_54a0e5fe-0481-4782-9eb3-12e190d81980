import {
	BaseRepository,
	BaseRepositoryImpl,
	CefrWordRepository,
	CefrWordRepositoryImpl,
	CollectionRepository,
	CollectionRepositoryImpl,
	CollectionStatsRepository,
	CollectionStatsRepositoryImpl,
	KeywordRepository,
	KeywordRepositoryImpl,
	LastSeenWordRepository,
	LastSeenWordRepositoryImpl,
	UserRepository,
	UserRepositoryImpl,
	WordRepository,
	WordRepositoryImpl,
} from '@/backend/repositories';
import { AuditRepository, AuditRepositoryImpl } from '@/backend/repositories/audit.repository';
import { SeoRepository, SeoRepositoryImpl } from '@/backend/repositories/seo.repository';
import {
	WordPackageRepository,
	WordPackageRepositoryImpl,
} from '@/backend/repositories/word-package.repository';
import {
	AdminService,
	AdminServiceImpl,
	AuditService,
	AuditServiceImpl,
	AuthService,
	AuthServiceImpl,
	CefrWordService,
	CefrWordServiceImpl,
	CollectionService,
	CollectionServiceImpl,
	CollectionStatsService,
	CollectionStatsServiceImpl,
	FeedbackService,
	FeedbackServiceImpl,
	KeywordService,
	KeywordServiceImpl,
	LastSeenWordService,
	LastSeenWordServiceImpl,
	TokenMonitorService,
	UserService,
	UserServiceImpl,
	WordService,
	WordServiceImpl,
} from '@/backend/services';
import { SeoService, SeoServiceImpl } from '@/backend/services/seo.service';
import {
	WordPackageService,
	WordPackageServiceImpl,
} from '@/backend/services/word-package.service';
import { AuditHelper, createAuditHelper } from '@/backend/utils/audit.helper';
import { buildOptimizedDatabaseUrl, getDatabaseConfig } from '@/config/database.config';
import { Feedback, PrismaClient } from '@prisma/client';
import { ICacheService } from './cache-init.server';
import { LLMService } from './services/llm.service';
import { WordNetService, WordNetServiceImpl } from './services/wordnet.service';

// Global variable to prevent multiple instances in development
declare global {
	var __prisma: PrismaClient | undefined;
}

let prismaClient: PrismaClient;

/**
 * Get or create a PrismaClient instance with proper connection pooling
 * Handles Next.js development hot reloading to prevent connection exhaustion
 */
export const getPrismaClient = (): PrismaClient => {
	if (prismaClient) {
		return prismaClient;
	}

	// In development, use global variable to prevent multiple instances during hot reload
	if (process.env.NODE_ENV === 'development') {
		if (global.__prisma) {
			prismaClient = global.__prisma;
			return prismaClient;
		}
	}

	// Get database configuration and create optimized client
	try {
		const dbConfig = getDatabaseConfig();
		const optimizedUrl = buildOptimizedDatabaseUrl(dbConfig.url, dbConfig);

		prismaClient = new PrismaClient({
			log: dbConfig.logLevel,
			datasources: {
				db: {
					url: optimizedUrl,
				},
			},
		});
	} catch (error) {
		console.warn('Failed to create optimized Prisma client, using fallback:', error);

		// Fallback configuration
		const dbUrl =
			process.env.DATABASE_URL ||
			'postgresql://postgres:postgres@localhost:5432/vocab?schema=public';
		const connectionLimit = process.env.NODE_ENV === 'development' ? 5 : 10;

		// Add connection limit to URL if not present
		try {
			const url = new URL(dbUrl);
			if (!url.searchParams.has('connection_limit')) {
				url.searchParams.set('connection_limit', connectionLimit.toString());
			}
			if (!url.searchParams.has('pool_timeout')) {
				url.searchParams.set('pool_timeout', '10');
			}

			prismaClient = new PrismaClient({
				log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
				datasources: {
					db: {
						url: url.toString(),
					},
				},
			});
		} catch (urlError) {
			// Final fallback
			prismaClient = new PrismaClient({
				log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
			});
		}
	}

	// Store in global variable for development
	if (process.env.NODE_ENV === 'development') {
		global.__prisma = prismaClient;
	}

	// Handle process termination gracefully
	const cleanup = async () => {
		console.log('Disconnecting Prisma client...');
		try {
			await prismaClient.$disconnect();
		} catch (error) {
			console.error('Error disconnecting Prisma client:', error);
		}
	};

	process.on('beforeExit', cleanup);
	process.on('SIGINT', cleanup);
	process.on('SIGTERM', cleanup);

	return prismaClient;
};

let cefrWordRepository: CefrWordRepository;
export const getCefrWordRepository = (): CefrWordRepository =>
	cefrWordRepository || (cefrWordRepository = new CefrWordRepositoryImpl(getPrismaClient()));

let collectionRepository: CollectionRepository;
export const getCollectionRepository = (): CollectionRepository =>
	collectionRepository ||
	(collectionRepository = new CollectionRepositoryImpl(getPrismaClient()));

let keywordRepository: KeywordRepository;
export const getKeywordRepository = (): KeywordRepository =>
	keywordRepository || (keywordRepository = new KeywordRepositoryImpl(getPrismaClient()));

let userRepository: UserRepository;
export const getUserRepository = (): UserRepository =>
	userRepository || (userRepository = new UserRepositoryImpl(getPrismaClient()));

let wordRepository: WordRepository;
export const getWordRepository = (): WordRepository =>
	wordRepository || (wordRepository = new WordRepositoryImpl(getPrismaClient()));

let lastSeenWordRepository: LastSeenWordRepository;
export const getLastSeenWordRepository = (): LastSeenWordRepository =>
	lastSeenWordRepository ||
	(lastSeenWordRepository = new LastSeenWordRepositoryImpl(getPrismaClient()));

let feedbackRepository: BaseRepository<Feedback>;
export const getFeedbackRepository = (): BaseRepository<Feedback> =>
	feedbackRepository ||
	(feedbackRepository = new BaseRepositoryImpl<Feedback>(getPrismaClient().feedback));

let collectionStatsRepository: CollectionStatsRepository;
export const getCollectionStatsRepository = (): CollectionStatsRepository =>
	collectionStatsRepository ||
	(collectionStatsRepository = new CollectionStatsRepositoryImpl(getPrismaClient()));

let auditRepository: AuditRepository;
export const getAuditRepository = (): AuditRepository =>
	auditRepository || (auditRepository = new AuditRepositoryImpl(getPrismaClient()));

let wordPackageRepository: WordPackageRepository;
export const getWordPackageRepository = (): WordPackageRepository =>
	wordPackageRepository ||
	(wordPackageRepository = new WordPackageRepositoryImpl(getPrismaClient()));

let userService: UserService | null = null;
export const getUserService = (): UserService =>
	userService || (userService = new UserServiceImpl(getUserRepository, getAuditHelper));

let authService: AuthService | null = null;
export const getAuthService = (): AuthService =>
	authService || (authService = new AuthServiceImpl(getUserService, getAuditService));

let cefrWordService: CefrWordService | null = null;
export const getCefrWordService = (): CefrWordService =>
	cefrWordService ||
	(cefrWordService = new CefrWordServiceImpl(getCefrWordRepository, getAuditHelper));

let cacheService: ICacheService | null = null;
export const getCacheService = async (): Promise<ICacheService> => {
	if (!cacheService) {
		cacheService = await getCacheServiceFromInit();
	}
	return cacheService!;
};

let feedbackService: FeedbackService | null = null;
export const getFeedbackService = (): FeedbackService =>
	feedbackService ||
	(feedbackService = new FeedbackServiceImpl(getFeedbackRepository, getAuditHelper));

let lastSeenWordService: LastSeenWordService | null = null;
export const getLastSeenWordService = (): LastSeenWordService =>
	lastSeenWordService ||
	(lastSeenWordService = new LastSeenWordServiceImpl(getLastSeenWordRepository));

let llmService: LLMService | null = null;
export const getLLMService = async (): Promise<LLMService> => {
	if (!llmService) {
		llmService = new LLMService(getWordService);
	}
	return llmService!;
};

let wordService: WordService | null = null;
export const getWordService = (): WordService =>
	wordService ||
	(wordService = new WordServiceImpl(
		getWordRepository,
		getCollectionService,
		getLastSeenWordService,
		getAuditHelper
	));

let collectionService: CollectionService | null = null;
export const getCollectionService = (): CollectionService =>
	collectionService ||
	(collectionService = new CollectionServiceImpl(
		getCollectionRepository,
		getLLMService, // This will be handled by the CollectionService internally
		getWordService,
		getAuditService,
		getAuditHelper
	));

let keywordService: any = null;
export const getKeywordService = (): KeywordService =>
	keywordService || (keywordService = new KeywordServiceImpl(getKeywordRepository));

let collectionStatsService: CollectionStatsService | null = null;
export const getCollectionStatsService = (): CollectionStatsService =>
	collectionStatsService ||
	(collectionStatsService = new CollectionStatsServiceImpl(getCollectionStatsRepository));

let tokenMonitorService: TokenMonitorService | null = null;
export const getTokenMonitorService = (): TokenMonitorService =>
	tokenMonitorService || (tokenMonitorService = new TokenMonitorService());

let wordNetService: WordNetService | null = null;
export const getWordNetService = (): WordNetService =>
	wordNetService || (wordNetService = new WordNetServiceImpl());

let auditService: AuditService | null = null;
export const getAuditService = (): AuditService =>
	auditService || (auditService = new AuditServiceImpl(getUserRepository, getAuditRepository));

let auditHelper: AuditHelper | null = null;
export const getAuditHelper = (): AuditHelper =>
	auditHelper || (auditHelper = createAuditHelper(getAuditService));

let adminService: AdminService | null = null;
export const getAdminService = (): AdminService =>
	adminService ||
	(adminService = new AdminServiceImpl(
		getUserService,
		getFeedbackService,
		getFeedbackRepository,
		getCacheService,
		getTokenMonitorService,
		getAuditService,
		getAuditHelper,
		getCollectionService
	));

// SEO Repository
let seoRepository: SeoRepository | null = null;
export const getSeoRepository = (): SeoRepository =>
	seoRepository || (seoRepository = new SeoRepositoryImpl());

// SEO Service
let seoService: SeoService | null = null;
export const getSeoService = (): SeoService =>
	seoService || (seoService = new SeoServiceImpl(getSeoRepository));

// Word Package Service
let wordPackageService: WordPackageService | null = null;
export const getWordPackageService = (): WordPackageService =>
	wordPackageService ||
	(wordPackageService = new WordPackageServiceImpl(
		getWordPackageRepository,
		getCollectionService,
		getWordService
	));

function getCacheServiceFromInit(): any {
	throw new Error('Function not implemented.');
}
